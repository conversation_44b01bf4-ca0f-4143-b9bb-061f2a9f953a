generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                               String                   @id @default(uuid())
  name                             String
  email                            String                   @unique
  password                         String?
  phone_number                     String?
  rm_code                          String                   @unique
  role_id                          String
  branch_id                        String
  last_login                       DateTime?
  last_password_update             DateTime?
  call_mtd_target                  Int                      @default(0)
  visit_mtd_target                 Int                      @default(0)
  target_related_calls             Int                      @default(0)
  target_related_visits            Int                      @default(0)
  created_at                       DateTime                 @default(now())
  updated_at                       DateTime                 @updatedAt
  activities                       Activity[]
  general_activities               GeneralActivity[]
  hitlist_calls                    HitlistCallLog[]
  hitlist_assignments              HitlistEntry[]           @relation("HitlistAssignedTo")
  hitlist_uploads                  HitlistEntry[]           @relation("HitlistUploadedBy")
  leads                            Lead[]
  assigned_leads                   Lead[]                   @relation("LeadAssignedUser")
  loan_activities                  LoanActivity[]
  scheduled_visits                 ScheduledVisit[]
  loan_clients                     LoanClient[]             @relation("LoanClientRmUser")
  assigned_loan_clients            LoanClient[]             @relation("LoanClientAssignedUser")
  targets                          Target[]
  exempted_targets                 ExemptedTargetUser[]
  customer_service_hitlist_uploads CustomerServiceHitlist[]
  two_by_two_phases                TwoByTwoPhase[]
  refresh_tokens                   RefreshToken[]
  otps                             Otp[]
  mfa_methods                      UserMfaMethod[]
  target_progress                  TargetProgress[]
  customer_categories              CustomerCategory[]
  customer_feedback_categories     CustomerFeedbackCategory[]
  branch                           Branch                   @relation(fields: [branch_id], references: [id])
  role                             Role                     @relation(fields: [role_id], references: [id])

  @@map("users")
}

model Role {
  id               String           @id @default(uuid())
  name             String
  description      String?
  role_permissions RolePermission[]
  users            User[]
  targets          Target[]

  @@map("roles")
}

model Permission {
  id               String           @id
  name             String
  description      String?
  role_permissions RolePermission[]

  @@map("permissions")
}

model RolePermission {
  role_id       String
  permission_id String
  created_at    DateTime?  @default(now())
  permission    Permission @relation(fields: [permission_id], references: [id])
  role          Role       @relation(fields: [role_id], references: [id])

  @@id([role_id, permission_id])
  @@map("role_permissions")
}

model Region {
  id         String    @id @default(uuid())
  name       String
  created_at DateTime? @default(now())
  updated_at DateTime? @updatedAt
  branches   Branch[]

  @@map("regions")
}

model Branch {
  id           String       @id @default(uuid())
  name         String
  region_id    String
  created_at   DateTime     @default(now())
  region       Region       @relation(fields: [region_id], references: [id])
  leads        Lead[]
  users        User[]
  loan_clients LoanClient[]
  targets      Target[]

  @@map("branches")
}

model CustomerCategory {
  id           String       @id @default(uuid())
  name         String
  added_by     String
  created_at   DateTime     @default(now())
  updated_at   DateTime?     @updatedAt
  leads        Lead[]
  loan_clients LoanClient[]
  user         User         @relation(fields: [added_by], references: [id])

  @@map("customer_categories")
}

model ISICSector {
  id           String       @id @default(uuid())
  code         String?
  name         String
  created_at   DateTime     @default(now())
  updated_at   DateTime     @updatedAt
  leads        Lead[]
  loan_clients LoanClient[]

  @@map("isic_sectors")
}

model Employer {
  id           String       @id @default(uuid())
  name         String
  leads        Lead[]
  loan_clients LoanClient[]

  @@map("employers")
}

model AnchorRelationship {
  id           String       @id @default(uuid())
  name         String
  leads        Lead[]
  loan_clients LoanClient[]

  @@map("anchor_relationships")
}

model Anchor {
  id           String       @id @default(uuid())
  name         String
  email        String
  phone_number String
  account_id   String?      @unique
  created_at   DateTime     @default(now())
  updated_at   DateTime     @updatedAt
  leads        Lead[]
  loan_clients LoanClient[]

  @@map("anchors")
}

model Lead {
  id                   String  @id @default(uuid())
  customer_name        String?
  anchor_id            String?
  customer_category_id String?
  isic_sector_id       String?
  phone_number         String?
  type_of_lead         String?
  branch_id            String?
  rm_user_id           String?
  assigned_user        String?

  account_number             String?    @unique
  account_number_assigned_at DateTime?  @map("account_number_assigned_at")
  employer_id                String?
  anchor_relationship_id     String?
  lead_status                String?
  created_at                 DateTime   @default(now())
  updated_at                 DateTime   @default(now()) @updatedAt
  activities                 Activity[]

  contact_persons     LeadContactPerson[]
  anchor              Anchor?             @relation(fields: [anchor_id], references: [id])
  anchor_relationship AnchorRelationship? @relation(fields: [anchor_relationship_id], references: [id])
  branch              Branch?             @relation(fields: [branch_id], references: [id])
  customer_category   CustomerCategory?   @relation(fields: [customer_category_id], references: [id])
  employer            Employer?           @relation(fields: [employer_id], references: [id])
  isic_sector         ISICSector?         @relation(fields: [isic_sector_id], references: [id])
  rm_user             User?               @relation(fields: [rm_user_id], references: [id])
  assigned_user_rel   User?               @relation("LeadAssignedUser", fields: [assigned_user], references: [rm_code])

  scheduled_visits ScheduledVisit[]

  @@map("leads")
}

model LeadContactPerson {
  id           String @id @default(uuid())
  lead_id      String
  name         String
  phone_number String
  lead         Lead   @relation(fields: [lead_id], references: [id])

  @@map("lead_contact_persons")
}

model GeneralActivity {
  id String @id @default(uuid())

  activity_type        String?
  purpose_id           String
  activity_date        DateTime?
  via_api              Boolean?
  api_call_reference   String?
  call_status          String?
  comment              String?
  next_visit_date      DateTime?
  status_id            String
  business_segment     String?
  performed_by_user_id String
  attachments          ActivityAttachment[]

  performed_by User              @relation(fields: [performed_by_user_id], references: [id])
  purpose      PurposeOfActivity @relation(fields: [purpose_id], references: [id])
  status       VisitStatus       @relation(fields: [status_id], references: [id])

  @@map("general_activities")
}

model ActivityAttachment {
  id                  String           @id @default(uuid())
  general_activity_id String?
  activity_id         String?
  loan_activity_id    String?
  file_url            String?
  created_at          DateTime         @default(now())
  updated_at          DateTime         @updatedAt
  general_activity    GeneralActivity? @relation(fields: [general_activity_id], references: [id])
  activity            Activity?        @relation(fields: [activity_id], references: [id])
  loan_activity       LoanActivity?    @relation(fields: [loan_activity_id], references: [id])

  @@map("activity_attachments")
}

model PurposeCategory {
  id                    String              @id @default(uuid())
  name                  String
  description           String?
  created_at            DateTime            @default(now())
  purpose_of_activities PurposeOfActivity[]

  @@map("purpose_categories")
}

model PurposeOfActivity {
  id                  String            @id @default(uuid())
  name                String?
  description         String?
  purpose_category_id String
  created_at          DateTime          @default(now())
  purpose_category    PurposeCategory   @relation(fields: [purpose_category_id], references: [id])
  general_activities  GeneralActivity[]
  activities          Activity[]
  loan_activities     LoanActivity[]

  @@map("purpose_of_activities")
}

model VisitStatus {
  id                 String            @id @default(uuid())
  status             String?
  description        String?
  general_activities GeneralActivity[]

  @@map("visit_statuses")
}

model HitlistEntry {
  id                  String    @id @default(uuid())
  uploaded_at         DateTime?
  uploaded_by_user_id String
  hitlist_type        String?
  file_reference      String?

  account_type         String?
  account_open_date    DateTime?
  planned_contact_date DateTime?
  status               String?
  assigned_to_user_id  String
  call_logs            HitlistCallLog[]
  assigned_to          User             @relation("HitlistAssignedTo", fields: [assigned_to_user_id], references: [id])

  uploaded_by User @relation("HitlistUploadedBy", fields: [uploaded_by_user_id], references: [id])

  @@map("hitlist_entries")
}

model HitlistFeedbackCategory {
  id        String           @id @default(uuid())
  name      String?
  call_logs HitlistCallLog[]

  @@map("hitlist_feedback_categories")
}

model HitlistCallLog {
  id                   String                  @id @default(uuid())
  hitlist_entry_id     String
  call_date            DateTime?
  called_by_user_id    String
  via_api              Boolean?
  api_call_reference   String?
  feedback_category_id String
  comment              String?
  called_by            User                    @relation(fields: [called_by_user_id], references: [id])
  feedback_category    HitlistFeedbackCategory @relation(fields: [feedback_category_id], references: [id])
  hitlist_entry        HitlistEntry            @relation(fields: [hitlist_entry_id], references: [id])

  @@map("hitlist_call_logs")
}

model LoanActivity {
  id                  String               @id @default(uuid())
  loan_client_id      String?
  interaction_type    String?
  call_status         String?
  visit_status        String?
  loan_account_number String?
  next_followup_date  DateTime?
  followup_status     String?              @default("pending")
  purpose_id          String?
  loan_balance        Decimal?
  arrears_days        Int?
  comment             String?
  call_duration_minutes             Int?
  rm_user_id          String
  created_at          DateTime             @default(now())
  updated_at          DateTime             @updatedAt
  via_api             Boolean?
  api_call_reference  String?

  rm_user     User                 @relation(fields: [rm_user_id], references: [id])
  loan_client LoanClient?          @relation(fields: [loan_client_id], references: [id])
  purpose     PurposeOfActivity?   @relation(fields: [purpose_id], references: [id])
  attachments ActivityAttachment[]

  @@map("loan_activities")
}

model ScheduledVisit {
  id                 String    @id @default(uuid())
  lead_id            String
  scheduled_by       String
  scheduled_for      DateTime?
  status             String?
  via_api            Boolean?
  api_call_reference String?
  lead               Lead      @relation(fields: [lead_id], references: [id])
  scheduled_by_user  User      @relation(fields: [scheduled_by], references: [id])

  @@map("scheduled_visits")
}

model Target {
  id                    String               @id @default(uuid())
  metric_type           MetricType
  target_value          Int
  frequency             Frequency
  activity              ActivityType         @default(LEADS_HITLIST)
  start_date            DateTime
  end_date              DateTime?
  user_id               String?
  role_id               String?
  branch_id             String               
  status                TargetStatus         @default(Active)
  archived_at           DateTime?
  created_at            DateTime             @default(now())
  updated_at            DateTime             @updatedAt
  user                  User?                @relation(fields: [user_id], references: [id])
  role                  Role?                @relation(fields: [role_id], references: [id])
  branch                Branch               @relation(fields: [branch_id], references: [id])
  exempted_target_users ExemptedTargetUser[]
  target_progress       TargetProgress[]

  @@map("targets")
}

model ExemptedTargetUser {
  id          String   @id @default(uuid())
  user_id     String
  target_id   String
  exempted_at DateTime @default(now())
  user        User     @relation(fields: [user_id], references: [id])
  target      Target   @relation(fields: [target_id], references: [id])

  @@unique([user_id, target_id])
  @@map("exempted_target_users")
}

enum MetricType {
  Call
  Visit
}

enum Frequency {
  daily
  weekly
  custom
}

enum TargetStatus {
  Active
  Expired
  Archived
}

  enum ActivityType {
    LEADS_HITLIST
    CUSTOMER_RELATIONSHIP
    TWO_BY_TWO_BY_TWO_HITLIST
    DORMANCY_HITLIST
    LOAN_ACTIVITIES
  }

model TargetProgress {
  id             String   @id @default(uuid())
  target_id      String
  user_id        String
  period_start   DateTime
  period_end     DateTime
  achieved_count Int      @default(0)
  target_value   Int      @default(0)
  is_achieved    Boolean? // null until evaluated
  is_applicable  Boolean  @default(true) // false for weekends/holidays

  target Target @relation(fields: [target_id], references: [id])
  user   User   @relation(fields: [user_id], references: [id])

  @@unique([target_id, user_id, period_start])
  @@map("target_progress")
}

model Holiday {
  id          String   @id @default(uuid())
  date        DateTime // store only the date part
  description String?
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  @@unique([date])
  @@map("holidays")
}

enum TwoByTwoPhaseType {
  first2
  second2
  third2
}

model Activity {
  id                                 String                        @id @default(uuid())
  lead_id                            String?
  activity_type                      String
  interaction_type                   String?
  call_status                        String?
  visit_status                       String?
  notes                              String?
  call_duration_minutes              Int?
  next_followup_date                 DateTime?
  followup_status                    String?                       @default("pending")
  performed_by_user_id               String
  purpose_id                         String?
  customer_service_hitlist_record_id String?
  two_by_two_phase_id                String?
  customer_feedback_id               String?
  created_at                         DateTime                      @default(now())
  updated_at                         DateTime                      @updatedAt
  attachments                        ActivityAttachment[]
  lead                               Lead?                         @relation(fields: [lead_id], references: [id])
  performed_by                       User                          @relation(fields: [performed_by_user_id], references: [id])
  purpose                            PurposeOfActivity?            @relation(fields: [purpose_id], references: [id])
  customer_service_hitlist_record    CustomerServiceHitlistRecord? @relation(fields: [customer_service_hitlist_record_id], references: [id])
  two_by_two_phase                   TwoByTwoPhase?                @relation(fields: [two_by_two_phase_id], references: [id])
  customer_feedback                  CustomerFeedbackCategory?     @relation(fields: [customer_feedback_id], references: [id])

  @@map("activities")
}

model CustomerServiceHitlist {
  id            String                         @id @default(uuid())
  code          String
  type          String
  uploaded_by   String
  uploaded_date DateTime
  records       CustomerServiceHitlistRecord[]
  uploader      User                           @relation(fields: [uploaded_by], references: [id])

  @@map("customer_service_hitlists")
}

model CustomerServiceHitlistRecord {
  id                          String                 @id @default(uuid())
  customer_service_hitlist_id String
  customer_name               String
  account_number              String
  phone_number                String
  rm_code                     String
  branch_code                 String
  activities                  Activity[]
  two_by_two_phases           TwoByTwoPhase[]
  customer_service_hitlist    CustomerServiceHitlist @relation(fields: [customer_service_hitlist_id], references: [id])

  @@map("customer_service_hitlist_records")
}

model TwoByTwoPhase {
  id                                 String                       @id @default(uuid())
  type                               TwoByTwoPhaseType
  customer_service_hitlist_record_id String
  assigned_to_user_id                String
  status                             String
  execution_date                     DateTime?
  expected_completion_date           DateTime?
  is_current                         Boolean                      @default(false)
  is_completed                       Boolean                      @default(false)
  is_verified_workday                Boolean                      @default(false)
  created_at                         DateTime                     @default(now())
  updated_at                         DateTime                     @updatedAt
  customer_service_hitlist_record    CustomerServiceHitlistRecord @relation(fields: [customer_service_hitlist_record_id], references: [id])
  assigned_to                        User                         @relation(fields: [assigned_to_user_id], references: [id])
  activities                         Activity[]

  @@map("two_by_two_phases")
}

model RefreshToken {
  id         String   @id @default(uuid())
  token      String   @unique
  user_id    String
  expires_at DateTime
  created_at DateTime @default(now())
  user       User     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@map("refresh_tokens")
}

model CustomerFeedbackCategory {
  id         String     @id @default(uuid())
  name       String
  added_by   String    
  created_at DateTime   @default(now())
  updated_at DateTime?   @updatedAt
  activities Activity[]
  user       User       @relation(fields: [added_by], references: [id])

  @@map("customer_feedback_categories")
}

model LoanClient {
  id                   String  @id @default(uuid())
  customer_name        String?
  anchor_id            String?
  customer_category_id String?
  isic_sector_id       String?
  phone_number         String?
  type_of_lead         String?
  branch_id            String?
  rm_user_id           String?
  assigned_user        String?

  account_number             String?        @unique
  account_number_assigned_at DateTime?      @map("account_number_assigned_at")
  employer_id                String?
  anchor_relationship_id     String?
  lead_status                String?
  created_at                 DateTime       @default(now())
  updated_at                 DateTime       @updatedAt
  loan_activities            LoanActivity[]

  contact_persons     LoanClientContactPerson[]
  anchor              Anchor?                   @relation(fields: [anchor_id], references: [id])
  anchor_relationship AnchorRelationship?       @relation(fields: [anchor_relationship_id], references: [id])
  branch              Branch?                   @relation(fields: [branch_id], references: [id])
  customer_category   CustomerCategory?         @relation(fields: [customer_category_id], references: [id])
  employer            Employer?                 @relation(fields: [employer_id], references: [id])
  isic_sector         ISICSector?               @relation(fields: [isic_sector_id], references: [id])
  rm_user             User?                     @relation("LoanClientRmUser", fields: [rm_user_id], references: [id])
  assigned_user_rel   User?                     @relation("LoanClientAssignedUser", fields: [assigned_user], references: [id])

  @@map("loan_clients")
}

model LoanClientContactPerson {
  id             String     @id @default(uuid())
  loan_client_id String
  name           String
  phone_number   String
  loan_client    LoanClient @relation(fields: [loan_client_id], references: [id])

  @@map("loan_client_contact_persons")
}

model Otp {
  id                 String         @id @default(cuid())
  code               String
  purpose            String // e.g., 'login', 'reset_password', 'mfa_verification'
  created_at         DateTime       @default(now())
  expires_at         DateTime
  used               Boolean        @default(false)
  user_id            String?
  user_mfa_method_id String?
  user               User?          @relation(fields: [user_id], references: [id])
  user_mfa_method    UserMfaMethod? @relation(fields: [user_mfa_method_id], references: [id])

  @@map("otps")
}

enum MfaType {
  EMAIL
  SMS
}

model MfaMethod {
  id           String          @id @default(uuid())
  method       MfaType
  description  String
  active       Boolean         @default(false)
  created_at   DateTime        @default(now())
  updated_at   DateTime        @updatedAt
  user_methods UserMfaMethod[]

  @@map("mfa_methods")
}

model UserMfaMethod {
  id           String    @id @default(uuid())
  user_id      String
  method_id    String
  contact      String // email or phone number
  enabled      Boolean   @default(false)
  verified     Boolean   @default(false)
  added_at     DateTime  @default(now())
  last_used_at DateTime?
  user         User      @relation(fields: [user_id], references: [id])
  method       MfaMethod @relation(fields: [method_id], references: [id])
  otps         Otp[]

  @@unique([user_id, method_id])
  @@map("user_mfa_methods")
}
