import { PrismaClient } from '@prisma/client';
import { seedAnchorRelationships } from './seeders/anchor-relationships.seeder';
import { seedMfaMethods } from './seeders/mfa-methods.seeder';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // First, let's create permissions if they don't exist
  const existingPermissions = await prisma.permission.findMany();

  if (existingPermissions.length < 100) {
    console.log('🔐 Creating permissions...');

    const permissionsData = [
      {
        id: 'users.create',
        name: 'Create Users',
        description: 'User Management',
      },
      {
        id: 'users.import',
        name: 'Import Users',
        description: 'User Management',
      },
      {
        id: 'users.export.print',
        name: 'Export and Print Users',
        description: 'User Management',
      },
      {
        id: 'users.view',
        name: 'View Users',
        description: 'User Management',
      },
      {
        id: 'users.update',
        name: 'Update Users',
        description: 'User Management',
      },
      {
        id: 'users.delete',
        name: 'Delete Users',
        description: 'User Management',
      },
      {
        id: 'roles.create',
        name: 'Create Roles',
        description: 'Role Management',
      },

      {
        id: 'roles.export.print',
        name: 'Export and Print Roles',
        description: 'Role Management',
      },
      {
        id: 'roles.view',
        name: 'View Roles',
        description: 'Role Management',
      },
      {
        id: 'roles.update',
        name: 'Update Roles',
        description: 'Role Management',
      },
      {
        id: 'roles.delete',
        name: 'Delete Roles',
        description: 'administration',
      },
      {
        id: 'targets.create',
        name: 'Create Targets',
        description: 'Target Management',
      },
      {
        id: 'targets.export.print',
        name: 'Export and Print Targets',
        description: 'Target Management',
      },
      {
        id: 'targets.view',
        name: 'View All Targets',
        description: 'Target Management',
      },
      {
        id: 'targets.update',
        name: 'Update Targets',
        description: 'Target Management',
      },
      {
        id: 'targets.delete',
        name: 'Delete Targets',
        description: 'Target Management',
      },
      {
        id: 'customer.service.hitlists.view',
        name: 'View Hitlists',
        description: 'Customer Service',
      },
      {
        id: 'customer.service.import',
        name: 'Import Hitlists',
        description: 'Customer Service',
      },
      {
        id: 'customer.service.export.print',
        name: 'Export & Print Hitlists',
        description: 'Customer Service',
      },
      {
        id: 'customer.service.personal.calls.view',
        name: 'View Personal Calls',
        description: 'Customer Service',
      },
      {
        id: 'view.all.leads',
        name: 'View All Leads',
        description: 'leads',
      },
      {
        id: 'view.my.leads',
        name: 'View My Leads',
        description: 'leads',
      },
      {
        id: 'leads.edit',
        name: 'Edit Leads',
        description: 'leads',
      },
      {
        id: 'leads.delete',
        name: 'Delete Leads',
        description: 'leads',
      },
      {
        id: 'leads.create',
        name: 'Add  Leads',
        description: 'leads',
      },
      {
        id: 'leads.call',
        name: 'Call a Lead',
        description: 'leads',
      },
      {
        id: 'leads.visit',
        name: 'Visit Leads',
        description: 'leads',
      },
      {
        id: 'leads.change.status',
        name: 'Change Lead Status',
        description: 'leads',
      },
      {
        id: 'leads.convert.to.client',
        name: 'Convert Leads to Client',
        description: 'leads',
      },
      {
        id: 'leads.view.calls',
        name: 'View Calls on Leads',
        description: 'leads',
      },
      {
        id: 'leads.view.visits',
        name: 'View Visits on Leads',
        description: 'leads',
      },
      {
        id: 'leads.view.followups',
        name: 'View Followups on Leads',
        description: 'leads',
      },
      {
        id: 'anchors.view',
        name: 'View Anchors',
        description: 'anchors',
      },
      {
        id: 'anchors.edit',
        name: 'Edit Anchors',
        description: 'anchors',
      },
      {
        id: 'anchors.delete',
        name: 'Delete Anchors',
        description: 'anchors',
      },
      {
        id: 'anchors.create',
        name: 'Add Anchors',
        description: 'anchors',
      },
      {
        id: 'view.all.customers',
        name: 'View All Customers',
        description: 'customers',
      },
      {
        id: 'view.my.customers',
        name: 'View My Customers',
        description: 'customers',
      },
      {
        id: 'customers.edit',
        name: 'Edit Customers',
        description: 'customers',
      },
      {
        id: 'customers.delete',
        name: 'Delete Customers',
        description: 'customers',
      },
      {
        id: 'customers.create',
        name: 'Add Customers',
        description: 'customers',
      },

      {
        id: 'regions.view',
        name: 'View Regions',
        description: 'items',
      },
      {
        id: 'regions.edit',
        name: 'Edit Regions ',
        description: 'items',
      },
      {
        id: 'regions.delete',
        name: 'Delete Regions ',
        description: 'items',
      },
      {
        id: 'regions.create',
        name: 'Add Regions ',
        description: 'items',
      },
      {
        id: 'branches.view',
        name: 'View   Branches',
        description: 'items',
      },
      {
        id: 'branches.edit',
        name: 'Edit Branches ',
        description: 'items',
      },
      {
        id: 'branches.delete',
        name: 'Delete Branches ',
        description: 'items',
      },
      {
        id: 'branches.create',
        name: 'Add Branches ',
        description: 'items',
      },
      {
        id: 'purpose.categories.view',
        name: 'View   Purpose Categories',
        description: 'items',
      },
      {
        id: 'purpose.categories.edit',
        name: 'Edit Purpose Categories ',
        description: 'items',
      },
      {
        id: 'purpose.categories.delete',
        name: 'Delete Purpose Categories ',
        description: 'items',
      },
      {
        id: 'purpose.categories.create',
        name: 'Add Purpose Categories ',
        description: 'items',
      },
      {
        id: 'purposes.view',
        name: 'View   Purposes',
        description: 'items',
      },
      {
        id: 'purposes.edit',
        name: 'Edit Purposes ',
        description: 'items',
      },
      {
        id: 'purposes.delete',
        name: 'Delete Purposes  ',
        description: 'items',
      },
      {
        id: 'purposes.create',
        name: 'Add Purposes  ',
        description: 'items',
      },
      {
        id: 'customer.categories.view',
        name: 'View   Customer Categories',
        description: 'items',
      },
      {
        id: 'customer.categories.edit',
        name: 'Edit Customer Categories ',
        description: 'items',
      },
      {
        id: 'customer.categories.delete',
        name: 'Delete Customer Categories ',
        description: 'items',
      },
      {
        id: 'customer.categories.create',
        name: 'Add Customer Categories ',
        description: 'items',
      },
      {
        id: 'customer.feedback.category.view',
        name: 'View   Customer Feedback Categories',
        description: 'items',
      },
      {
        id: 'customer.feedback.category.edit',
        name: 'Edit Customer Feedback Category ',
        description: 'items',
      },
      {
        id: 'customer.feedback.category.delete',
        name: 'Delete Customer Feedback Category ',
        description: 'items',
      },
      {
        id: 'customer.feedback.category.create',
        name: 'Add Customer Feedback Category ',
        description: 'items',
      },
      {
        id: 'isic.sector.view',
        name: 'View   ISIC Sectors',
        description: 'items',
      },
      {
        id: 'isic.sector.edit',
        name: 'Edit ISIC Sectors ',
        description: 'items',
      },
      {
        id: 'isic.sector.delete',
        name: 'Delete ISIC Sectors  ',
        description: 'items',
      },
      {
        id: 'isic.sector.create',
        name: 'Add ISIC Sectors ',
        description: 'items',
      },
    ];

    // Create permissions in bulk
    await prisma.permission.createMany({
      data: permissionsData,
      skipDuplicates: true,
    });

    console.log(
      `✅ Created ${permissionsData.length} permissions: ${permissionsData.map((p) => p.name).join(', ')}`,
    );
  } else {
    console.log(
      `🔐 Permissions already exist (${existingPermissions.length} found)`,
    );
  }

  // Now, let's create some regions if they don't exist
  const existingRegions = await prisma.region.findMany();

  let centralRegion,
    northEasternRegion,
    nyanzaRegion,
    westernRegion,
    easternRegion,
    nairobiRegion,
    coastalRegion,
    riftValleyRegion;

  if (existingRegions.length < 8) {
    console.log('📍 Creating regions...');

    centralRegion = await prisma.region.create({
      data: {
        name: 'Central Region',
      },
    });

    northEasternRegion = await prisma.region.create({
      data: {
        name: 'North Eastern Region',
      },
    });

    nyanzaRegion = await prisma.region.create({
      data: {
        name: 'Nyanza Region',
      },
    });
    westernRegion = await prisma.region.create({
      data: {
        name: 'Western Region',
      },
    });

    easternRegion = await prisma.region.create({
      data: {
        name: 'Eastern Region',
      },
    });

    nairobiRegion = await prisma.region.create({
      data: {
        name: 'Nairobi Region',
      },
    });

    coastalRegion = await prisma.region.create({
      data: {
        name: 'Coastal Region',
      },
    });

    riftValleyRegion = await prisma.region.create({
      data: {
        name: 'Rift Valley Region',
      },
    });

    console.log(
      `✅ Created regions: ${centralRegion.name}, ${northEasternRegion.name}, ${nyanzaRegion.name}, ${westernRegion.name}, ${easternRegion.name}, ${nairobiRegion.name}, ${coastalRegion.name}, ${riftValleyRegion.name}`,
    );
  } else {
    // Use existing regions
    centralRegion = existingRegions[0];
    northEasternRegion = existingRegions[1];
    nyanzaRegion = existingRegions[2];
    westernRegion = existingRegions[3];
    easternRegion = existingRegions[4];
    nairobiRegion = existingRegions[5];
    coastalRegion = existingRegions[6];
    riftValleyRegion = existingRegions[7];
    console.log(
      `📍 Using existing regions: ${centralRegion.name}, ${northEasternRegion.name}, ${nyanzaRegion.name}, ${westernRegion.name}, ${easternRegion.name}, ${nairobiRegion.name}, ${coastalRegion.name}, ${riftValleyRegion.name}`,
    );
  }

  // Check if branches already exist
  const existingBranches = await prisma.branch.findMany();

  if (existingBranches.length < 23) {
    console.log('🏢 Creating test branches...');

    const branch1 = await prisma.branch.create({
      data: {
        name: 'Thika Branch',
        region_id: centralRegion.id,
      },
    });

    const branch2 = await prisma.branch.create({
      data: {
        name: 'Nakuru Branch',
        region_id: riftValleyRegion.id,
      },
    });

    const branch3 = await prisma.branch.create({
      data: {
        name: 'Meru Branch',
        region_id: centralRegion.id,
      },
    });

    const branch4 = await prisma.branch.create({
      data: {
        name: 'Kisumu Branch',
        region_id: nyanzaRegion.id,
      },
    });

    const branch5 = await prisma.branch.create({
      data: {
        name: 'Kitale Branch',
        region_id: riftValleyRegion.id,
      },
    });

    const branch6 = await prisma.branch.create({
      data: {
        name: 'Kayole Branch',
        region_id: nairobiRegion.id,
      },
    });

    const branch7 = await prisma.branch.create({
      data: {
        name: 'Gikomba Branch',
        region_id: nairobiRegion.id,
      },
    });

    const branch8 = await prisma.branch.create({
      data: {
        name: 'Wangige Branch',
        region_id: centralRegion.id,
      },
    });

    const branch9 = await prisma.branch.create({
      data: {
        name: 'Ongata Rongai Branch',
        region_id: riftValleyRegion.id,
      },
    });

    const branch10 = await prisma.branch.create({
      data: {
        name: 'Mtwapa Branch',
        region_id: coastalRegion.id,
      },
    });

    const branch11 = await prisma.branch.create({
      data: {
        name: 'Machakos Branch',
        region_id: easternRegion.id,
      },
    });

    const branch12 = await prisma.branch.create({
      data: {
        name: 'Kisii Branch',
        region_id: nyanzaRegion.id,
      },
    });

    const branch13 = await prisma.branch.create({
      data: {
        name: ' Kikuyu Branch',
        region_id: centralRegion.id,
      },
    });

    const branch14 = await prisma.branch.create({
      data: {
        name: 'Kawangware Branch',
        region_id: nairobiRegion.id,
      },
    });

    const branch15 = await prisma.branch.create({
      data: {
        name: 'Eldoret Branch',
        region_id: riftValleyRegion.id,
      },
    });

    const branch16 = await prisma.branch.create({
      data: {
        name: 'Utawala Branch',
        region_id: nairobiRegion.id,
      },
    });

    const branch17 = await prisma.branch.create({
      data: {
        name: ' Nyeri Branch',
        region_id: centralRegion.id,
      },
    });

    const branch18 = await prisma.branch.create({
      data: {
        name: ' Mombasa Branch',
        region_id: coastalRegion.id,
      },
    });

    const branch19 = await prisma.branch.create({
      data: {
        name: ' Kirinyaga Road Branch',
        region_id: nairobiRegion.id,
      },
    });

    const branch20 = await prisma.branch.create({
      data: {
        name: ' Kitengela Branch',
        region_id: easternRegion.id,
      },
    });

    const branch21 = await prisma.branch.create({
      data: {
        name: ' Kiambu Branch',
        region_id: centralRegion.id,
      },
    });

    const branch22 = await prisma.branch.create({
      data: {
        name: ' Koinange Branch',
        region_id: nairobiRegion.id,
      },
    });

    const branch23 = await prisma.branch.create({
      data: {
        name: ' Main Office',
        region_id: nairobiRegion.id,
      },
    });
  } else {
    console.log(`🏢 Branches already exist (${existingBranches.length} found)`);
  }

  // Check if customer categories already exist
  const existingCustomerCategories = await prisma.customerCategory.findMany();

  if (existingCustomerCategories.length === 0) {
    console.log('🏷️ Creating test customer categories...');

    // Get an existing user to use as the creator
    const existingUser = await prisma.user.findFirst();
    if (!existingUser) {
      console.log('⚠️ No users found, skipping customer category creation');
    } else {
      const category1 = await prisma.customerCategory.create({
        data: {
          name: 'Corporate',
          added_by: existingUser.id,
        },
      });

      const category2 = await prisma.customerCategory.create({
        data: {
          name: 'Small Business',
          added_by: existingUser.id,
        },
      });

      const category3 = await prisma.customerCategory.create({
        data: {
          name: 'Individual',
          added_by: existingUser.id,
        },
      });

      console.log(
        `✅ Created customer categories: ${category1.name}, ${category2.name}, ${category3.name}`,
      );
    }
  } else {
    console.log(
      `🏷️ Customer categories already exist (${existingCustomerCategories.length} found)`,
    );
  }

  // Check if employers already exist
  const existingEmployers = await prisma.employer.findMany();

  if (existingEmployers.length === 0) {
    console.log('🏭 Creating test employers...');

    const employer1 = await prisma.employer.create({
      data: {
        name: 'Tech Solutions Ltd',
      },
    });

    const employer2 = await prisma.employer.create({
      data: {
        name: 'Manufacturing Corp',
      },
    });

    const employer3 = await prisma.employer.create({
      data: {
        name: 'Healthcare Services Inc',
      },
    });

    console.log(
      `✅ Created employers: ${employer1.name}, ${employer2.name}, ${employer3.name}`,
    );
  } else {
    console.log(
      `🏭 Employers already exist (${existingEmployers.length} found)`,
    );
  }

  // Check if users already exist
  const existingUsers = await prisma.user.findMany();

  if (existingUsers.length === 0) {
    console.log('👥 Creating test users...');

    // First, we need to create some roles
    const existingRoles = await prisma.role.findMany();
    let rmRole;

    if (existingRoles.length === 0) {
      rmRole = await prisma.role.create({
        data: {
          name: 'Relationship Manager',
          description: 'Manages customer relationships and leads',
        },
      });
      console.log(`✅ Created role: ${rmRole.name}`);
    } else {
      rmRole = existingRoles[0];
    }

    // Get the branches to assign users to
    const branches = await prisma.branch.findMany();
    const branch1 = branches[0];
    const branch2 = branches.length > 1 ? branches[1] : branches[0];

    // Hash the default password
    const hashedPassword = await bcrypt.hash('defaultPassword123', 12);

    const user1 = await prisma.user.create({
      data: {
        name: 'John Smith',
        email: '<EMAIL>',
        password: hashedPassword,
        phone_number: '+254712345678',
        rm_code: 'RM001',
        role_id: rmRole.id,
        branch_id: branch1.id,
      },
    });

    const user2 = await prisma.user.create({
      data: {
        name: 'Jane Doe',
        email: '<EMAIL>',
        password: hashedPassword,
        phone_number: '+254787654321',
        rm_code: 'RM002',
        role_id: rmRole.id,
        branch_id: branch2.id,
      },
    });

    console.log(`✅ Created users: ${user1.name}, ${user2.name}`);

    // Assign lead permissions to the Relationship Manager role
    const leadPermissions = ['view.all.leads', 'view.my.leads'];

    for (const permissionId of leadPermissions) {
      await prisma.rolePermission.create({
        data: {
          role_id: rmRole.id,
          permission_id: permissionId,
        },
      });
    }

    console.log(`✅ Assigned lead permissions to ${rmRole.name} role`);
  } else {
    console.log(`👥 Users already exist (${existingUsers.length} found)`);
  }

  // Check if anchors already exist
  const existingAnchors = await prisma.anchor.findMany();

  if (existingAnchors.length === 0) {
    console.log('⚓ Creating test anchors...');

    const anchor1 = await prisma.anchor.create({
      data: {
        name: 'ABC Corporation',
        email: '<EMAIL>',
        phone_number: '0712345678',
      },
    });

    const anchor2 = await prisma.anchor.create({
      data: {
        name: 'XYZ Holdings Ltd',
        email: '<EMAIL>',
        phone_number: '0723456789',
      },
    });

    const anchor3 = await prisma.anchor.create({
      data: {
        name: 'Kenya Business Partners',
        email: '<EMAIL>',
        phone_number: '0734567890',
      },
    });

    const anchor4 = await prisma.anchor.create({
      data: {
        name: 'East Africa Enterprises',
        email: '<EMAIL>',
        phone_number: '0745678901',
      },
    });

    const anchor5 = await prisma.anchor.create({
      data: {
        name: 'Nairobi Investment Group',
        email: '<EMAIL>',
        phone_number: '0756789012',
      },
    });

    const anchor6 = await prisma.anchor.create({
      data: {
        name: 'Mombasa Trade Center',
        email: '<EMAIL>',
        phone_number: '0767890123',
      },
    });

    const anchor7 = await prisma.anchor.create({
      data: {
        name: 'Kisumu Development Corp',
        email: '<EMAIL>',
        phone_number: '0778901234',
      },
    });

    const anchor8 = await prisma.anchor.create({
      data: {
        name: 'Eldoret Agricultural Hub',
        email: '<EMAIL>',
        phone_number: '0789012345',
      },
    });

    const anchor9 = await prisma.anchor.create({
      data: {
        name: 'Nakuru Manufacturing Alliance',
        email: '<EMAIL>',
        phone_number: '0790123456',
      },
    });

    const anchor10 = await prisma.anchor.create({
      data: {
        name: 'Thika Industrial Park',
        email: '<EMAIL>',
        phone_number: '0701234567',
      },
    });

    console.log(
      `✅ Created anchors: ${anchor1.name}, ${anchor2.name}, ${anchor3.name}, ${anchor4.name}, ${anchor5.name}, ${anchor6.name}, ${anchor7.name}, ${anchor8.name}, ${anchor9.name}, ${anchor10.name}`,
    );
  } else {
    console.log(`⚓ Anchors already exist (${existingAnchors.length} found)`);
  }

  // Seed anchor relationships
  await seedAnchorRelationships();

  // Seed MFA methods
  await seedMfaMethods();

  // Check if ISIC sectors already exist
  const existingIsicSectors = await prisma.iSICSector.findMany();

  if (existingIsicSectors.length === 0) {
    console.log('🏭 Creating test ISIC sectors...');

    const sector1 = await prisma.iSICSector.create({
      data: {
        code: 'A01',
        name: 'Agriculture and Forestry',
      },
    });

    const sector2 = await prisma.iSICSector.create({
      data: {
        code: 'C10',
        name: 'Manufacturing',
      },
    });

    const sector3 = await prisma.iSICSector.create({
      data: {
        code: 'J62',
        name: 'Information Technology',
      },
    });

    console.log(
      `✅ Created ISIC sectors: ${sector1.name}, ${sector2.name}, ${sector3.name}`,
    );
  } else {
    console.log(
      `🏭 ISIC sectors already exist (${existingIsicSectors.length} found)`,
    );
  }

  console.log('🎉 Database seeding completed!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
