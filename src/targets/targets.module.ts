import { Modu<PERSON> } from '@nestjs/common';
import { TargetsController } from './targets.controller';
import { TargetsService } from './targets.service';
import { TargetProgressService } from './services/target-progress.service';
import { TargetUpdateService } from './services/target-update.service';
import { DailyTargetHandler } from './handlers/daily-target.handler';
import { PrismaModule } from '../prisma/prisma.module';

/**
 * Module for handling target-related functionality
 * Provides target creation and management capabilities
 */
@Module({
  imports: [PrismaModule],
  controllers: [TargetsController],
  providers: [
    TargetsService,
    TargetProgressService,
    TargetUpdateService,
    DailyTargetHandler,
  ],
  exports: [
    TargetsService,
    TargetProgressService,
    TargetUpdateService,
    DailyTargetHandler,
  ],
})
export class TargetsModule {}
