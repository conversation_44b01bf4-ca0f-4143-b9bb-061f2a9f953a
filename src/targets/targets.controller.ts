import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  HttpCode,
  HttpStatus,
  ValidationPipe,
  ParseUUIDPipe,
  UseGuards,
  Request,
  Query,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiCreatedResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { TargetsService } from './targets.service';
import { TargetUpdateService } from './services/target-update.service';
import { TargetGenerationService } from './services/target-generation.service';
import { CreateTargetDto } from './dto/create-target.dto';
import { UpdateTargetDto } from './dto/update-target.dto';
import { CreateIndividualTargetDto } from './dto/create-individual-target.dto';
import { TargetResponseDto } from './dto/target-response.dto';
import { TargetBreakdownResponseDto } from './dto/target-breakdown-response.dto';
import { MyTargetsResponseDto } from './dto/my-targets-response.dto';

/**
 * Controller handling target-related HTTP endpoints
 * Provides endpoints for creating and retrieving targets
 */
@ApiTags('Targets')
@Controller('targets')
export class TargetsController {
  constructor(
    private readonly targetsService: TargetsService,
    private readonly targetUpdateService: TargetUpdateService,
    private readonly targetGenerationService: TargetGenerationService,
  ) {}

  /**
   * Creates targets based on shared input
   * POST /targets
   */
  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create targets',
    description:
      'Creates targets based on shared input. For each ID in assignTo, creates a Target with the same data and assigns either roleId or userId appropriately based on scope.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Targets created successfully',
    type: TargetResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data',
  })
  @ApiNotFoundResponse({
    description: 'One or more roles/users not found',
  })
  async createTargets(
    @Body(ValidationPipe) createTargetDto: CreateTargetDto,
    @Request() req: any,
  ): Promise<TargetResponseDto> {
    return this.targetsService.createTargets(
      createTargetDto,
      req.user.branch_id,
    );
  }

  /**
   * Gets all targets formatted for UI display
   * GET /targets
   */
  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get all targets',
    description:
      'Returns all targets formatted for UI display. Each target includes progress calculation and scope information.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Targets retrieved successfully',
    type: [TargetResponseDto],
  })
  async getAllTargets(
    @Request() req: any,
    @Query('status') status?: string,
  ): Promise<TargetResponseDto[]> {
    return this.targetsService.getAllTargets(req.user.branch_id, status);
  }

  /**
   * Updates a target by ID
   * PATCH /targets/:id
   */
  @Patch(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update target',
    description:
      'Updates a target by ID with the provided fields. Only the fields provided in the request body will be updated.',
  })
  @ApiParam({ name: 'id', description: 'Target UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Target updated successfully',
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or start date is not before end date',
  })
  @ApiNotFoundResponse({
    description: 'Target not found',
  })
  async updateTarget(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateTargetDto: UpdateTargetDto,
  ): Promise<void> {
    return this.targetsService.updateTarget(id, updateTargetDto);
  }

  /**
   * Deletes a target by ID
   * DELETE /targets/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete target',
    description: 'Deletes a target by ID.',
  })
  @ApiParam({ name: 'id', description: 'Target UUID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Target deleted successfully',
  })
  @ApiNotFoundResponse({
    description: 'Target not found',
  })
  async deleteTarget(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.targetsService.deleteTarget(id);
  }

  /**
   * Gets breakdown of users for a role target
   * GET /targets/:id/breakdown
   */
  @Get(':id/breakdown')
  @ApiOperation({
    summary: 'Get target breakdown',
    description:
      'Gets breakdown of all users for a role target, showing their individual call and visit counts within the target period.',
  })
  @ApiParam({ name: 'id', description: 'Target UUID (must be a role target)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Target breakdown retrieved successfully',
    type: [TargetBreakdownResponseDto],
  })
  @ApiNotFoundResponse({
    description: 'Target not found',
  })
  @ApiBadRequestResponse({
    description: 'Target breakdown is only available for role targets',
  })
  async getTargetBreakdown(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<TargetBreakdownResponseDto[]> {
    return this.targetsService.getTargetBreakdown(id);
  }

  /**
   * Creates an individual target from an existing target
   * POST /targets/:id/create-individual-target
   */
  @Post(':id/create-individual-target')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create individual target from existing target',
    description:
      "Creates an individual target using the source target's frequency, start date, and end date. Assigns the new target to the specified user with the provided target value.",
  })
  @ApiParam({ name: 'id', description: 'Source target UUID' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Individual target created successfully',
    type: TargetResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or new target value is not a valid number',
  })
  @ApiNotFoundResponse({
    description: 'Source target or user not found',
  })
  async createIndividualTarget(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) createIndividualTargetDto: CreateIndividualTargetDto,
  ): Promise<TargetResponseDto> {
    return this.targetsService.createIndividualTarget(
      id,
      createIndividualTargetDto,
    );
  }

  /**
   * Demo endpoint to update user targets based on activity
   * @param body - Activity details
   * @returns Promise<{ message: string }>
   */
  @Post('update-progress')
  @ApiOperation({ summary: 'Update user target progress based on activity' })
  @ApiCreatedResponse({ description: 'Target progress updated successfully' })
  async updateUserTargets(
    @Body()
    body: {
      activity: string;
      interaction_type: string;
      user_id: string;
    },
  ): Promise<{ message: string }> {
    await this.targetUpdateService.updateUserTargets(
      body.activity,
      body.interaction_type,
      body.user_id,
    );
    return { message: 'Target progress updated successfully' };
  }

  /**
   * Get today's targets for the logged-in user
   * GET /targets/my-targets
   */
  @Get('my-targets')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: "Get today's targets for the logged-in user",
    description:
      'Returns all targets applicable to the user (by role or individual) for today, with their progress and applicability status.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Targets retrieved successfully',
    type: MyTargetsResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'User not found',
  })
  async getMyTargets(@Request() req: any): Promise<MyTargetsResponseDto> {
    return this.targetsService.getMyTargets(req.user.id);
  }

  /**
   * Generate today's target progress records for all role targets
   * POST /targets/generate-today-progress
   */
  @Post('generate-today-progress')
  @ApiOperation({
    summary: "Generate today's target progress records",
    description:
      'Creates TargetProgress records for all role targets with period from 4am to 10pm today',
  })
  @ApiCreatedResponse({
    description: 'Target progress records generated successfully',
  })
  async generateTodayTargetProgress(): Promise<{ message: string }> {
    await this.targetGenerationService.generateTodayTargetProgress();
    return {
      message: "Today's target progress records generated successfully",
    };
  }

  /**
   * Update MTD targets for all users
   * POST /targets/generate-users-mtd-target
   */
  @Post('generate-users-mtd-target')
  @ApiOperation({
    summary: 'Update MTD targets for all users',
    description:
      'Updates call_mtd_target and visit_mtd_target fields for all users based on their applicable targets',
  })
  @ApiCreatedResponse({ description: 'User MTD targets updated successfully' })
  async generateUsersMTDTarget(): Promise<{ message: string }> {
    await this.targetGenerationService.generateUsersMTDTarget();
    return { message: 'User MTD targets updated successfully' };
  }

  /**
   * Archives a target by ID
   * POST /targets/:id/archive
   */
  @Post(':id/archive')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Archive target',
    description:
      'Changes the target status to Archived and updates the archived_at field.',
  })
  @ApiParam({ name: 'id', description: 'Target UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Target archived successfully',
  })
  @ApiNotFoundResponse({
    description: 'Target not found',
  })
  async archiveTarget(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.targetsService.archiveTarget(id);
  }
}
