import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { isApplicable } from '../../common/utils/date.utils';

/**
 * Service for generating target progress records and updating user MTD targets
 */
@Injectable()
export class TargetGenerationService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Generate TargetProgress records for today for all role targets
   * Creates records with period from 4am to 10pm of today's date
   */
  async generateTodayTargetProgress(): Promise<void> {
    try {
      const today = new Date();

      // Set period start at 4am today
      const periodStart = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
        4, // 4am
        0,
        0,
        0,
      );

      // Set period end at 10pm today
      const periodEnd = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
        22, // 10pm
        0,
        0,
        0,
      );

      // Check if today is applicable (not weekend or holiday)
      const applicable = await isApplicable(today, this.prisma);

      // Get all active role targets (targets with role_id not null)
      const roleTargets = await this.prisma.target.findMany({
        where: {
          role_id: { not: null },
          status: { not: 'Archived' },
          OR: [{ end_date: null }, { end_date: { gte: today } }],
        },
        include: {
          exempted_target_users: true,
        },
      });

      const progressRecords: any[] = [];

      for (const target of roleTargets) {
        // Get users of this role in the same branch, excluding exempted users
        const exemptedUserIds = target.exempted_target_users.map(
          (etu) => etu.user_id,
        );

        const eligibleUsers = await this.prisma.user.findMany({
          where: {
            role_id: target.role_id!,
            branch_id: target.branch_id,
            id: { notIn: exemptedUserIds },
          },
        });

        // Create progress records for each eligible user
        for (const user of eligibleUsers) {
          // Check if record already exists for today
          const existingRecord = await this.prisma.targetProgress.findFirst({
            where: {
              target_id: target.id,
              user_id: user.id,
              period_start: {
                gte: new Date(
                  today.getFullYear(),
                  today.getMonth(),
                  today.getDate(),
                ),
                lt: new Date(
                  today.getFullYear(),
                  today.getMonth(),
                  today.getDate() + 1,
                ),
              },
            },
          });

          if (!existingRecord) {
            progressRecords.push({
              target_id: target.id,
              user_id: user.id,
              period_start: periodStart,
              period_end: periodEnd,
              achieved_count: 0,
              target_value: target.target_value,
              is_achieved: null,
              is_applicable: applicable,
            });
          }
        }
      }

      // Batch create all progress records
      if (progressRecords.length > 0) {
        await this.prisma.targetProgress.createMany({
          data: progressRecords,
          skipDuplicates: true,
        });

        console.log(
          `Created ${progressRecords.length} target progress records for today`,
        );
      }
    } catch (error) {
      console.error(
        `Error generating today's target progress: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Generate and update MTD targets for all users based on their applicable targets
   * Updates call_mtd_target and visit_mtd_target fields separately based on metric_type
   */
  async generateUsersMTDTarget(): Promise<void> {
    try {
      const today = new Date();
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

      // Get all users
      const users = await this.prisma.user.findMany({
        select: {
          id: true,
          role_id: true,
          branch_id: true,
        },
      });

      for (const user of users) {
        let callMtdTarget = 0;
        let visitMtdTarget = 0;

        // Get role-based targets for this user
        const roleTargets = await this.prisma.target.findMany({
          where: {
            role_id: user.role_id,
            branch_id: user.branch_id,
            status: { not: 'Archived' },
            exempted_target_users: {
              none: { user_id: user.id },
            },
          },
        });

        // Get individual targets for this user
        const individualTargets = await this.prisma.target.findMany({
          where: {
            user_id: user.id,
            status: { not: 'Archived' },
          },
        });

        // Combine all targets
        const allUserTargets = [...roleTargets, ...individualTargets];

        // Process each target
        for (const target of allUserTargets) {
          // Get applicable target progress records for this month
          const targetProgressSum = await this.prisma.targetProgress.aggregate({
            where: {
              target_id: target.id,
              user_id: user.id,
              is_applicable: true,
              period_start: {
                gte: startOfMonth,
                lte: endOfMonth,
              },
            },
            _sum: {
              target_value: true,
            },
          });

          const sumValue = targetProgressSum._sum?.target_value || 0;

          // Add to appropriate MTD target based on metric type
          if (target.metric_type === 'Call') {
            callMtdTarget += sumValue;
          } else if (target.metric_type === 'Visit') {
            visitMtdTarget += sumValue;
          }
        }

        // Update user's MTD targets
        await this.prisma.user.update({
          where: { id: user.id },
          data: {
            call_mtd_target: callMtdTarget,
            visit_mtd_target: visitMtdTarget,
          },
        });
      }

      console.log(`Updated MTD targets for ${users.length} users`);
    } catch (error) {
      console.error(`Error generating users MTD targets: ${error.message}`);
      throw error;
    }
  }
}
